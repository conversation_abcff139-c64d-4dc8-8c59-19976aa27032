package main

import (
	"fmt"
	"log"
	"time"

	plc4go "github.com/apache/plc4x/plc4go/pkg/api"
	"github.com/apache/plc4x/plc4go/pkg/api/drivers"
	"github.com/apache/plc4x/plc4go/pkg/api/model"
	"github.com/apache/plc4x/plc4go/spi/values"
)

func modbus() {
	// 创建驱动管理器
	driverManager := plc4go.NewPlcDriverManager()
	defer func() {
		if err := driverManager.Close(); err != nil {
			log.Printf("Error closing driver manager: %v", err)
		}
	}()

	// 演示不同的Modbus变体
	fmt.Println("=== Modbus协议示例 ===")

	// 1. Modbus TCP示例
	demonstrateModbusTcp(driverManager)

	// 2. Modbus RTU示例
	demonstrateModbusRtu(driverManager)

	// 3. Modbus ASCII示例
	demonstrateModbusAscii(driverManager)

	fmt.Println("所有Modbus操作完成!")
}

// 演示Modbus TCP
func demonstrateModbusTcp(driverManager plc4go.PlcDriverManager) {
	fmt.Println("\n=== Modbus TCP示例 ===")

	// 注册Modbus TCP驱动
	drivers.RegisterModbusTcpDriver(driverManager)

	// Modbus TCP连接字符串格式: modbus-tcp://host:port?unit-identifier=1
	// 默认端口: 502
	connectionString := "modbus-tcp://*************:502?unit-identifier=1"
	fmt.Printf("正在连接到Modbus TCP设备: %s\n", connectionString)

	// 获取连接
	connectionResult := <-driverManager.GetConnection(connectionString)
	if connectionResult.GetErr() != nil {
		log.Printf("Modbus TCP连接失败: %v", connectionResult.GetErr())
		return
	}

	connection := connectionResult.GetConnection()
	defer connection.BlockingClose()

	fmt.Println("Modbus TCP连接成功!")
	time.Sleep(500 * time.Millisecond)

	// 执行读写操作
	performModbusReadOperations(connection, "TCP")
	performModbusWriteOperations(connection, "TCP")
}

// 演示Modbus RTU
func demonstrateModbusRtu(driverManager plc4go.PlcDriverManager) {
	fmt.Println("\n=== Modbus RTU示例 ===")

	// 注册Modbus RTU驱动
	drivers.RegisterModbusRtuDriver(driverManager)

	// Modbus RTU连接字符串格式: modbus-rtu://COM1?baud-rate=9600&data-bits=8&parity=none&stop-bits=1&unit-identifier=1
	// Linux串口示例: modbus-rtu:///dev/ttyUSB0?baud-rate=9600&unit-identifier=1
	connectionString := "modbus-rtu:///dev/ttyUSB0?baud-rate=9600&data-bits=8&parity=none&stop-bits=1&unit-identifier=1"
	fmt.Printf("正在连接到Modbus RTU设备: %s\n", connectionString)

	// 获取连接
	connectionResult := <-driverManager.GetConnection(connectionString)
	if connectionResult.GetErr() != nil {
		log.Printf("Modbus RTU连接失败 (可能没有串口设备): %v", connectionResult.GetErr())
		return
	}

	connection := connectionResult.GetConnection()
	defer connection.BlockingClose()

	fmt.Println("Modbus RTU连接成功!")
	time.Sleep(500 * time.Millisecond)

	// 执行读写操作
	performModbusReadOperations(connection, "RTU")
	performModbusWriteOperations(connection, "RTU")
}

// 演示Modbus ASCII
func demonstrateModbusAscii(driverManager plc4go.PlcDriverManager) {
	fmt.Println("\n=== Modbus ASCII示例 ===")

	// 注册Modbus ASCII驱动
	drivers.RegisterModbusAsciiDriver(driverManager)

	// Modbus ASCII连接字符串格式类似RTU
	connectionString := "modbus-ascii:///dev/ttyUSB0?baud-rate=9600&data-bits=7&parity=even&stop-bits=1&unit-identifier=1"
	fmt.Printf("正在连接到Modbus ASCII设备: %s\n", connectionString)

	// 获取连接
	connectionResult := <-driverManager.GetConnection(connectionString)
	if connectionResult.GetErr() != nil {
		log.Printf("Modbus ASCII连接失败 (可能没有串口设备): %v", connectionResult.GetErr())
		return
	}

	connection := connectionResult.GetConnection()
	defer connection.BlockingClose()

	fmt.Println("Modbus ASCII连接成功!")
	time.Sleep(500 * time.Millisecond)

	// 执行读写操作
	performModbusReadOperations(connection, "ASCII")
	performModbusWriteOperations(connection, "ASCII")
}

// 执行Modbus读取操作
func performModbusReadOperations(connection plc4go.PlcConnection, variant string) {
	fmt.Printf("\n=== Modbus %s读取操作示例 ===\n", variant)

	// 创建读取请求构建器
	readRequestBuilder := connection.ReadRequestBuilder()

	// Modbus标签地址格式说明:
	// 1. PLC4X格式:
	//    - coil:address:datatype[quantity]           // 线圈 (0x区域)
	//    - discrete-input:address:datatype[quantity] // 离散输入 (1x区域)
	//    - input-register:address:datatype[quantity] // 输入寄存器 (3x区域)
	//    - holding-register:address:datatype[quantity] // 保持寄存器 (4x区域)
	//    - extended-register:address:datatype[quantity] // 扩展寄存器 (6x区域)
	//
	// 2. 数字格式:
	//    - 0x0001:BOOL    // 线圈地址1
	//    - 1x0001:BOOL    // 离散输入地址1
	//    - 3x0001:INT     // 输入寄存器地址1
	//    - 4x0001:INT     // 保持寄存器地址1

	// 添加各种类型的Modbus读取标签
	readRequestBuilder.
		AddTagAddress("coil_1", "coil:1:BOOL").                          // 读取线圈1
		AddTagAddress("coil_array", "coil:10:BOOL[8]").                  // 读取线圈10-17
		AddTagAddress("discrete_input", "discrete-input:1:BOOL").        // 读取离散输入1
		AddTagAddress("input_reg_int", "input-register:1:INT").          // 读取输入寄存器1 (16位整数)
		AddTagAddress("input_reg_real", "input-register:3:REAL").        // 读取输入寄存器3-4 (32位浮点)
		AddTagAddress("holding_reg_int", "holding-register:1:INT").      // 读取保持寄存器1
		AddTagAddress("holding_reg_dint", "holding-register:5:DINT").    // 读取保持寄存器5-6 (32位整数)
		AddTagAddress("holding_reg_array", "holding-register:10:INT[4]") // 读取保持寄存器10-13

	// 构建读取请求
	readRequest, err := readRequestBuilder.Build()
	if err != nil {
		log.Printf("构建Modbus读取请求失败: %v", err)
		return
	}

	// 执行读取请求
	fmt.Printf("执行Modbus %s读取请求...\n", variant)
	readResult := <-readRequest.Execute()
	if readResult.GetErr() != nil {
		log.Printf("Modbus %s读取请求执行失败: %v", variant, readResult.GetErr())
		return
	}

	// 处理读取响应
	readResponse := readResult.GetResponse()

	// 显示读取结果
	for _, tagName := range readResponse.GetTagNames() {
		if readResponse.GetResponseCode(tagName) == model.PlcResponseCode_OK {
			value := readResponse.GetValue(tagName)
			fmt.Printf("Modbus %s标签 %s: %v (类型: %T)\n", variant, tagName, value.String(), value.GetPlcValueType())
		} else {
			fmt.Printf("Modbus %s标签 %s 读取失败: %s\n", variant, tagName, readResponse.GetResponseCode(tagName))
		}
	}
}

// 执行Modbus写入操作
func performModbusWriteOperations(connection plc4go.PlcConnection, variant string) {
	fmt.Printf("\n=== Modbus %s写入操作示例 ===\n", variant)

	// 创建写入请求构建器
	writeRequestBuilder := connection.WriteRequestBuilder()
	
	// 添加各种类型的Modbus写入标签和值
	writeRequestBuilder.
		AddTagAddress("coil_write", "coil:100:BOOL", values.NewPlcBOOL(true)). // 写入线圈100
		AddTagAddress("coil_array_write", "coil:110:BOOL[4]",
			values.NewPlcList([]values.PlcValue{ // 写入线圈数组110-113
				values.NewPlcBOOL(true),
				values.NewPlcBOOL(false),
				values.NewPlcBOOL(true),
				values.NewPlcBOOL(false),
													})).
		AddTagAddress("holding_int", "holding-register:100:INT", values.NewPlcINT(1234)).       // 写入保持寄存器100
		AddTagAddress("holding_dint", "holding-register:102:DINT", values.NewPlcDINT(123456)).  // 写入保持寄存器102-103
		AddTagAddress("holding_real", "holding-register:104:REAL", values.NewPlcREAL(3.14159)). // 写入保持寄存器104-105
		AddTagAddress("holding_array", "holding-register:110:INT[3]",                           // 写入保持寄存器数组110-112
			values.NewPlcList([]values.PlcValue{
				values.NewPlcINT(100),
				values.NewPlcINT(200),
				values.NewPlcINT(300),
			}))

	// 构建写入请求
	writeRequest, err := writeRequestBuilder.Build()
	if err != nil {
		log.Printf("构建Modbus写入请求失败: %v", err)
		return
	}

	// 执行写入请求
	fmt.Printf("执行Modbus %s写入请求...\n", variant)
	writeResult := <-writeRequest.Execute()
	if writeResult.GetErr() != nil {
		log.Printf("Modbus %s写入请求执行失败: %v", variant, writeResult.GetErr())
		return
	}

	// 处理写入响应
	writeResponse := writeResult.GetResponse()

	// 显示写入结果
	for _, tagName := range writeResponse.GetTagNames() {
		if writeResponse.GetResponseCode(tagName) == model.PlcResponseCode_OK {
			fmt.Printf("Modbus %s标签 %s: 写入成功\n", variant, tagName)
		} else {
			fmt.Printf("Modbus %s标签 %s 写入失败: %s\n", variant, tagName, writeResponse.GetResponseCode(tagName))
		}
	}

	// 验证写入结果
	fmt.Printf("验证Modbus %s写入结果:\n", variant)
	verifyModbusWrites(connection, variant)
}

// 验证Modbus写入结果
func verifyModbusWrites(connection plc4go.PlcConnection, variant string) {
	readRequestBuilder := connection.ReadRequestBuilder()

	// 读取刚才写入的值进行验证
	readRequestBuilder.
		AddTagAddress("coil_verify", "coil:100:BOOL").
		AddTagAddress("holding_int_verify", "holding-register:100:INT").
		AddTagAddress("holding_real_verify", "holding-register:104:REAL")

	readRequest, err := readRequestBuilder.Build()
	if err != nil {
		log.Printf("构建验证读取请求失败: %v", err)
		return
	}

	readResult := <-readRequest.Execute()
	if readResult.GetErr() != nil {
		log.Printf("验证读取请求执行失败: %v", readResult.GetErr())
		return
	}

	readResponse := readResult.GetResponse()
	for _, tagName := range readResponse.GetTagNames() {
		if readResponse.GetResponseCode(tagName) == model.PlcResponseCode_OK {
			value := readResponse.GetValue(tagName)
			fmt.Printf("验证Modbus %s %s: %v\n", variant, tagName, value.String())
		} else {
			fmt.Printf("验证Modbus %s %s 失败: %s\n", variant, tagName, readResponse.GetResponseCode(tagName))
		}
	}
}
